#!/usr/bin/env python3
"""
WebSocket PING PONG时间间隔分析脚本
分析日志中PING PONG消息的时间间隔，检测超时情况
"""

import re
import sys
import argparse
from datetime import datetime, timedelta
from typing import List, Tuple, Optional


def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
    """解析日志时间戳"""
    try:
        # 格式: 2025-06-24 11:11:44
        return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return None


def read_log_lines(log_file: str) -> List[str]:
    """读取日志文件所有行"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            return f.readlines()
    except FileNotFoundError:
        print(f"错误: 日志文件 {log_file} 未找到")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取日志文件失败 - {e}")
        sys.exit(1)


def extract_ping_pong_events(lines: List[str]) -> List[Tuple[int, datetime, str]]:
    """提取PING PONG事件及其行号和时间戳"""
    events = []
    ping_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*< PING.*\[0 bytes\]')
    pong_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*> PONG.*\[0 bytes\]')
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检查PING消息
        ping_match = ping_pattern.search(line)
        if ping_match:
            timestamp = parse_timestamp(ping_match.group(1))
            if timestamp:
                events.append((i, timestamp, 'PING'))
        
        # 检查PONG消息
        pong_match = pong_pattern.search(line)
        if pong_match:
            timestamp = parse_timestamp(pong_match.group(1))
            if timestamp:
                events.append((i, timestamp, 'PONG'))
    
    return events


def analyze_ping_pong_intervals(events: List[Tuple[int, datetime, str]], 
                              timeout_threshold: float) -> List[Tuple[int, int, float, datetime, datetime]]:
    """分析PING PONG间隔，返回超时的情况"""
    timeouts = []
    
    i = 0
    while i < len(events) - 1:
        current_line, current_time, current_type = events[i]
        
        # 查找下一个PING事件
        if current_type == 'PING':
            # 查找对应的PONG
            next_i = i + 1
            while next_i < len(events):
                next_line, next_time, next_type = events[next_i]
                if next_type == 'PONG':
                    # 计算PING到PONG的时间间隔
                    interval = (next_time - current_time).total_seconds()
                    
                    if interval > timeout_threshold:
                        timeouts.append((current_line, next_line, interval, current_time, next_time))
                    break
                next_i += 1
        
        i += 1
    
    return timeouts


def print_context_logs(lines: List[str], start_line: int, end_line: int, context_lines: int = 10):
    """打印指定行周围的上下文日志"""
    total_lines = len(lines)
    
    # 计算上下文范围
    context_start = max(0, start_line - context_lines)
    context_end = min(total_lines, end_line + context_lines + 1)
    
    print(f"  上下文日志 (行 {context_start + 1} - {context_end}):")
    print("  " + "=" * 80)
    
    for i in range(context_start, context_end):
        prefix = ">>> " if start_line <= i <= end_line else "    "
        print(f"  {prefix}{i + 1:4d}: {lines[i].rstrip()}")
    
    print("  " + "=" * 80)


def main():
    parser = argparse.ArgumentParser(description='分析WebSocket PING PONG时间间隔')
    parser.add_argument('log_file', nargs='?', default='tmp/server.log', 
                       help='日志文件路径 (默认: tmp/server.log)')
    parser.add_argument('-t', '--timeout', type=float, default=3.0,
                       help='超时阈值（秒）(默认: 3.0)')
    parser.add_argument('-c', '--context', type=int, default=10,
                       help='显示上下文日志行数 (默认: 10)')
    
    args = parser.parse_args()
    
    print(f"分析日志文件: {args.log_file}")
    print(f"超时阈值: {args.timeout} 秒")
    print(f"上下文行数: {args.context}")
    print("-" * 60)
    
    # 读取日志文件
    lines = read_log_lines(args.log_file)
    print(f"总共读取 {len(lines)} 行日志")
    
    # 提取PING PONG事件
    events = extract_ping_pong_events(lines)
    ping_count = sum(1 for _, _, event_type in events if event_type == 'PING')
    pong_count = sum(1 for _, _, event_type in events if event_type == 'PONG')
    
    print(f"发现 {ping_count} 个PING事件, {pong_count} 个PONG事件")
    
    if not events:
        print("未发现PING PONG事件")
        return
    
    # 分析时间间隔
    timeouts = analyze_ping_pong_intervals(events, args.timeout)
    
    print(f"\n发现 {len(timeouts)} 次超过 {args.timeout} 秒的PING PONG间隔:")
    print("=" * 60)
    
    if not timeouts:
        print("✅ 所有PING PONG间隔都在正常范围内")
    else:
        for i, (ping_line, pong_line, interval, ping_time, pong_time) in enumerate(timeouts, 1):
            print(f"\n❌ 超时 #{i}: {interval:.3f} 秒")
            print(f"   PING时间: {ping_time} (行 {ping_line + 1})")
            print(f"   PONG时间: {pong_time} (行 {pong_line + 1})")
            print(f"   间隔: {interval:.3f} 秒 (超出阈值 {interval - args.timeout:.3f} 秒)")
            
            # 打印上下文日志
            print_context_logs(lines, ping_line, pong_line, args.context)
    
    # 统计信息
    if events:
        all_intervals = []
        i = 0
        while i < len(events) - 1:
            current_line, current_time, current_type = events[i]
            if current_type == 'PING':
                next_i = i + 1
                while next_i < len(events):
                    next_line, next_time, next_type = events[next_i]
                    if next_type == 'PONG':
                        interval = (next_time - current_time).total_seconds()
                        all_intervals.append(interval)
                        break
                    next_i += 1
            i += 1
        
        if all_intervals:
            avg_interval = sum(all_intervals) / len(all_intervals)
            max_interval = max(all_intervals)
            min_interval = min(all_intervals)
            
            print(f"\n📊 统计信息:")
            print(f"   总PING PONG对数: {len(all_intervals)}")
            print(f"   平均间隔: {avg_interval:.3f} 秒")
            print(f"   最大间隔: {max_interval:.3f} 秒")
            print(f"   最小间隔: {min_interval:.3f} 秒")
            print(f"   超时次数: {len(timeouts)}")
            print(f"   超时率: {len(timeouts)/len(all_intervals)*100:.1f}%")


if __name__ == "__main__":
    main()