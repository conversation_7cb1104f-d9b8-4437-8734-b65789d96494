from config.logger import setup_logging
import json
import re
from plugins_func.register import FunctionRegistry, ActionResponse, Action, ToolType
from plugins_func.functions.hass_init import append_devices_to_prompt
import time

TAG = __name__


def validate_and_fix_json(json_str, function_name=None, logger=None):
    """
    验证和修复 JSON 字符串
    返回一个元组 (success, result, error_message)
    success: 布尔值，表示是否成功
    result: 如果成功，返回解析后的字典；如果失败，返回修复后的字典或空字典
    error_message: 如果失败，返回错误消息
    """
    if not json_str or not isinstance(json_str, str):
        return True, {}, None
    
    try:
        # 尝试直接解析
        result = json.loads(json_str)
        return True, result, None
    except json.JSONDecodeError as e:
        if logger:
            logger.warning(f"JSON 解析错误: {e}")
        
        # 尝试修复常见错误
        fixed_json = json_str
        
        # 1. 特定函数的修复逻辑
        if function_name == "get_weather":
            # 处理 {"location": "lang": "zh_CN"} 格式
            pattern = r'\{\s*"location"\s*:\s*"lang"\s*:\s*"([^"]+)"\s*\}'
            match = re.search(pattern, json_str)
            if match:
                lang_value = match.group(1)
                fixed_json = '{"lang": "' + lang_value + '"}'
                if logger:
                    logger.info(f"修复 get_weather 参数: {fixed_json}")
                try:
                    return True, json.loads(fixed_json), None
                except:
                    pass
        
        # 2. 通用修复逻辑
        # 修复缺失的引号
        fixed_json = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)\s*:', r'\1"\2":', fixed_json)
        
        # 修复缺失的逗号
        fixed_json = re.sub(r'"\s*"', '":', fixed_json)
        fixed_json = re.sub(r'("[^"]*")\s*([^:,{}\[\]\s])', r'\1,\2', fixed_json)
        
        try:
            result = json.loads(fixed_json)
            if logger:
                logger.info(f"成功修复 JSON: {fixed_json}")
            return True, result, None
        except json.JSONDecodeError as e2:
            # 如果修复失败，返回默认值
            if function_name == "get_weather":
                return False, {"lang": "zh_CN"}, str(e2)
            return False, {}, str(e2)


class FunctionHandler:
    def __init__(self, conn):
        self.conn = conn
        self.config = conn.config
        self.function_registry = FunctionRegistry()
        self.register_nessary_functions()
        self.register_config_functions()
        self.functions_desc = self.function_registry.get_all_function_desc()
        func_names = self.current_support_functions()
        self.modify_plugin_loader_des(func_names)
        self.finish_init = True

    def modify_plugin_loader_des(self, func_names):
        if "plugin_loader" not in func_names:
            return
        # 可编辑的列表中去掉plugin_loader
        surport_plugins = [func for func in func_names if func != "plugin_loader"]
        func_names = ",".join(surport_plugins)
        for function_desc in self.functions_desc:
            if function_desc["function"]["name"] == "plugin_loader":
                function_desc["function"]["description"] = function_desc["function"][
                    "description"
                ].replace("[plugins]", func_names)
                break

    def upload_functions_desc(self):
        self.functions_desc = self.function_registry.get_all_function_desc()
        # 添加调试日志：显示更新后的函数列表
        func_names = [func["function"]["name"] for func in self.functions_desc]
        self.conn.logger.bind(tag=TAG).info(f"📋 函数描述已更新，当前函数列表: {func_names}")
        
        # 特别检查MCP函数
        mcp_funcs = [name for name in func_names if name.startswith('mcp_')]
        if mcp_funcs:
            self.conn.logger.bind(tag=TAG).info(f"🔧 MCP函数列表: {mcp_funcs}")
        else:
            self.conn.logger.bind(tag=TAG).warning(f"⚠️ 未发现MCP函数")

    def current_support_functions(self):
        func_names = []
        for func in self.functions_desc:
            func_names.append(func["function"]["name"])
        # 打印当前支持的函数列表
        self.conn.logger.bind(tag=TAG, session_id=self.conn.session_id).info(
            f"当前支持的函数列表: {func_names}"
        )
        return func_names

    def get_functions(self):
        """获取功能调用配置"""
        # 添加调试日志：显示发送给LLM的函数列表
        func_names = [func["function"]["name"] for func in self.functions_desc]
        self.conn.logger.bind(tag=TAG).info(f"📤 发送给LLM的函数列表: {func_names}")
        
        # 特别标注MCP函数
        mcp_funcs = [name for name in func_names if name.startswith('mcp_')]
        if mcp_funcs:
            self.conn.logger.bind(tag=TAG).info(f"🔧 包含的MCP函数: {mcp_funcs}")
            
        return self.functions_desc

    # 在函数前加 @register_function 只是说可以注册，当客户端连接上来以后，对于当前 session 注册需要在这里
    def register_nessary_functions(self):
        """注册必要的函数"""
        # 如果注册好了，会日志里看到有一条 函数 'xxx' 注册成功
        # self.function_registry.register_function("handle_exit_intent")
        self.function_registry.register_function("plugin_loader")
        self.function_registry.register_function("get_time")
        self.function_registry.register_function("get_lunar")
        # self.function_registry.register_function("handle_device")
        self.function_registry.register_function("play_music_url")

    def register_config_functions(self):
        """注册配置中的函数,可以不同客户端使用不同的配置"""
        for func in self.config["Intent"][self.config["selected_module"]["Intent"]].get(
            "functions", []
        ):
            self.function_registry.register_function(func)

        """home assistant需要初始化提示词"""
        append_devices_to_prompt(self.conn)

    def get_function(self, name):
        return self.function_registry.get_function(name)

    def handle_llm_function_call(self, conn, function_call_data):
        function_name = None
        try:
            begin_time = time.time() * 1000
            function_name = function_call_data["name"]
            
            # 添加调试日志：显示LLM尝试调用的函数
            self.conn.logger.bind(tag=TAG).info(f"🔄 LLM尝试调用函数: {function_name}")
            
            # 如果是MCP函数，添加特别标注
            if function_name.startswith('mcp_'):
                self.conn.logger.bind(tag=TAG).info(f"🔧 这是一个MCP函数调用: {function_name}")
            
            funcItem = self.get_function(function_name)
            if not funcItem:
                # 添加调试日志：显示函数未找到的详细信息
                available_funcs = [func["function"]["name"] for func in self.functions_desc]
                self.conn.logger.bind(tag=TAG).error(f"❌ 函数未找到: {function_name}")
                self.conn.logger.bind(tag=TAG).error(f"📋 当前可用函数: {available_funcs}")
                
                return ActionResponse(
                    action=Action.NOTFOUND, result="抱歉，我还没有学会如何处理这个问题", response=""
                )
            func = funcItem.func
            arguments = function_call_data["arguments"]
            
            # 使用通用参数验证和修复函数
            success, arguments, error_msg = validate_and_fix_json(
                arguments, function_name, self.conn.logger.bind(tag=TAG)
            )
            
            if not success:
                self.conn.logger.bind(tag=TAG).warning(
                    f"参数验证失败，使用修复后的参数: {arguments}, 错误: {error_msg}"
                )
            
            self.conn.logger.bind(tag=TAG).debug(
                f"调用函数: {function_name}, 参数: {arguments}"
            )
            
            if (
                funcItem.type == ToolType.SYSTEM_CTL
                or funcItem.type == ToolType.IOT_CTL
            ):
                return func(conn, **arguments)
            elif funcItem.type == ToolType.WAIT:
                return func(**arguments)
            elif funcItem.type == ToolType.CHANGE_SYS_PROMPT:
                return func(conn, **arguments)
            else:
                return ActionResponse(
                    action=Action.NOTFOUND, result="抱歉，我还没有学会如何处理这个问题", response=""
                )
        except Exception as e:
            self.conn.logger.bind(tag=TAG).error(f"处理function call错误: {e}")
        finally:
            if function_name:  # 确保 function_name 已定义
                end_time = time.time() * 1000
                self.conn.logger.bind(tag=TAG).info(f"处理 {function_name} 延迟: {end_time - begin_time}毫秒")
            
        return None
