#!/usr/bin/env python3
"""
MP3 会议记录转写和摘要工具
使用 Google Gemini 2.5 Flash 模型处理音频文件，生成会议纪要和发言详细记录
"""

import os
import sys
import argparse
import asyncio
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from google import genai
from google.genai import types
from config.logger import setup_logging

# 配置日志
logger = setup_logging()
TAG = __name__

class MP3Summarizer:
    """MP3 会议记录处理器"""
    
    def __init__(self, api_key: str, model: str = "gemini-2.5-flash"):
        """初始化处理器"""
        self.api_key = api_key
        self.model = model
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # 初始化 Gemini 客户端
        self.client = genai.Client(
            api_key=self.api_key,
            http_options=types.HttpOptions(timeout=120000)  # 2分钟超时
        )
        
        logger.bind(tag=TAG).info(f"MP3Summarizer initialized with model: {self.model}")
    
    def validate_mp3_file(self, file_path: str) -> bool:
        """验证MP3文件"""
        if not os.path.exists(file_path):
            logger.bind(tag=TAG).error(f"文件不存在: {file_path}")
            return False
        
        if not file_path.lower().endswith('.mp3'):
            logger.bind(tag=TAG).error(f"文件不是MP3格式: {file_path}")
            return False
        
        file_size = os.path.getsize(file_path)
        max_size = 100 * 1024 * 1024  # 100MB限制
        
        if file_size > max_size:
            logger.bind(tag=TAG).error(f"文件太大: {file_size} bytes (最大 {max_size} bytes)")
            return False
        
        logger.bind(tag=TAG).info(f"MP3文件验证通过: {file_path} ({file_size} bytes)")
        return True
    
    async def process_mp3(self, mp3_path: str, output_dir: str = None) -> dict:
        """处理MP3文件并生成会议纪要"""
        try:
            # 验证文件
            if not self.validate_mp3_file(mp3_path):
                return {"success": False, "error": "文件验证失败"}
            
            # 设置输出目录
            if not output_dir:
                output_dir = os.path.dirname(mp3_path)
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 读取音频文件
            logger.bind(tag=TAG).info(f"开始处理音频文件: {mp3_path}")
            
            with open(mp3_path, 'rb') as f:
                audio_bytes = f.read()
            
            # 创建音频部分
            audio_part = types.Part.from_bytes(
                data=audio_bytes,
                mime_type="audio/mp3"
            )
            
            # 生成会议纪要
            logger.bind(tag=TAG).info("开始生成会议纪要...")
            summary_result = await self._generate_summary(audio_part)
            
            # 生成发言详细记录
            logger.bind(tag=TAG).info("开始生成发言详细记录...")
            transcript_result = await self._generate_transcript(audio_part)
            
            # 保存结果到文件
            results = await self._save_results(
                mp3_path, output_dir, summary_result, transcript_result
            )
            
            return {
                "success": True,
                "files": results,
                "summary_length": len(summary_result),
                "transcript_length": len(transcript_result)
            }
            
        except Exception as e:
            error_msg = f"处理MP3文件时发生错误: {str(e)}"
            logger.bind(tag=TAG).error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _generate_summary(self, audio_part) -> str:
        """生成会议纪要"""
        system_prompt = """你是一个专业的会议纪要整理助手，需要将音频内容整理成规范的会议纪要。

要求：
1. 仔细听取音频内容，提取关键信息
2. 按照标准会议纪要格式组织内容
3. 包括：会议主题、主要讨论内容、决议事项、行动计划等
4. 语言要简洁明了，条理清晰
5. 突出重点决议和后续行动
6. 如果音频质量不佳或信息不完整，请根据现有信息尽力整理

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：[从音频中提取或标注为当前时间]
- 会议主题：[从音频中提取]
- 参会人员：[从音频中识别]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要。"""

        user_prompt = "请分析这段会议音频，生成完整的会议纪要："

        try:
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )

            # 生成内容
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=[user_prompt, audio_part],
                config=generation_config
            )

            return response.text if hasattr(response, 'text') and response.text else "无法生成会议纪要"

        except Exception as e:
            logger.bind(tag=TAG).error(f"生成会议纪要失败: {str(e)}")
            return f"生成会议纪要时发生错误：{str(e)}"

    async def _generate_transcript(self, audio_part) -> str:
        """生成发言详细记录"""
        system_prompt = """你是一个专业的会议转录助手，需要将音频内容转录成详细的发言记录。

要求：
1. 尽可能准确地转录所有发言内容
2. 区分不同的发言人（如果能够识别）
3. 保留发言的原始语气和表达方式
4. 标注重要的停顿、笑声等非语言信息
5. 按时间顺序整理发言内容
6. 如果无法清晰听到某些内容，请标注[听不清]

转录格式：
# 会议发言详细记录

## 转录说明
- 转录时间：[当前时间]
- 音频质量：[评估音频清晰度]
- 参与人数：[估计发言人数量]

## 详细转录

### [时间段] 发言人A：
[具体发言内容]

### [时间段] 发言人B：
[具体发言内容]

[继续按发言顺序记录...]

## 补充说明
[对转录过程中遇到的问题或需要说明的地方进行备注]

请尽可能详细和准确地转录音频内容。"""

        user_prompt = "请将这段会议音频转录成详细的发言记录："

        try:
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )

            # 生成内容
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=[user_prompt, audio_part],
                config=generation_config
            )

            return response.text if hasattr(response, 'text') and response.text else "无法生成发言记录"

        except Exception as e:
            logger.bind(tag=TAG).error(f"生成发言记录失败: {str(e)}")
            return f"生成发言记录时发生错误：{str(e)}"

    async def _save_results(self, mp3_path: str, output_dir: str, summary: str, transcript: str) -> list:
        """保存结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(mp3_path))[0]
        
        files = []
        
        try:
            # 保存会议纪要
            summary_file = os.path.join(output_dir, f"{base_name}_会议纪要_{timestamp}.md")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"# 会议纪要 - {base_name}\n\n")
                f.write(f"**音频文件**: {os.path.basename(mp3_path)}\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**处理模型**: {self.model}\n\n")
                f.write("---\n\n")
                f.write(summary)
            
            files.append(summary_file)
            logger.bind(tag=TAG).info(f"会议纪要已保存: {summary_file}")
            
            # 保存发言详细记录
            transcript_file = os.path.join(output_dir, f"{base_name}_发言记录_{timestamp}.md")
            with open(transcript_file, 'w', encoding='utf-8') as f:
                f.write(f"# 发言详细记录 - {base_name}\n\n")
                f.write(f"**音频文件**: {os.path.basename(mp3_path)}\n")
                f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**处理模型**: {self.model}\n\n")
                f.write("---\n\n")
                f.write(transcript)
            
            files.append(transcript_file)
            logger.bind(tag=TAG).info(f"发言记录已保存: {transcript_file}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存文件失败: {str(e)}")
            raise
        
        return files


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MP3 会议记录转写和摘要工具")
    parser.add_argument("mp3_file", help="MP3 音频文件路径")
    parser.add_argument("--api-key", required=True, help="Google Gemini API Key")
    parser.add_argument("--model", default="gemini-2.5-flash", help="Gemini 模型名称 (默认: gemini-2.5-flash)")
    parser.add_argument("--output-dir", help="输出目录 (默认: 与MP3文件同目录)")
    
    args = parser.parse_args()
    
    # 验证MP3文件路径
    if not os.path.exists(args.mp3_file):
        print(f"错误: 文件不存在 - {args.mp3_file}")
        sys.exit(1)
    
    # 创建处理器
    try:
        summarizer = MP3Summarizer(api_key=args.api_key, model=args.model)
    except Exception as e:
        print(f"错误: 初始化失败 - {str(e)}")
        sys.exit(1)
    
    # 处理文件
    print(f"开始处理MP3文件: {args.mp3_file}")
    print(f"使用模型: {args.model}")
    print("=" * 50)
    
    result = await summarizer.process_mp3(
        mp3_path=args.mp3_file,
        output_dir=args.output_dir
    )
    
    if result["success"]:
        print("✅ 处理完成！")
        print(f"📄 会议纪要长度: {result['summary_length']} 字符")
        print(f"📝 发言记录长度: {result['transcript_length']} 字符")
        print("\n生成的文件:")
        for file_path in result["files"]:
            print(f"  - {file_path}")
    else:
        print(f"❌ 处理失败: {result['error']}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())