import os, json, uuid
from types import SimpleNamespace
from typing import Any, Dict, List

import requests
from google import genai
from google.genai import types

from core.providers.llm.base import LLMProviderBase
from core.utils.util import check_model_key
from config.logger import setup_logging
from requests import RequestException

log = setup_logging()
TAG = __name__


def test_proxy(proxy_url: str, test_url: str) -> bool:
    try:
        resp = requests.get(test_url, proxies={"http": proxy_url, "https": proxy_url})
        return 200 <= resp.status_code < 400
    except RequestException:
        return False


def setup_proxy_env(http_proxy: str | None, https_proxy: str | None):
    """
    分别测试 HTTP 和 HTTPS 代理是否可用，并设置环境变量。
    如果 HTTPS 代理不可用但 HTTP 可用，会将 HTTPS_PROXY 也指向 HTTP。
    """
    test_http_url = "http://www.google.com"
    test_https_url = "https://www.google.com"

    ok_http = ok_https = False

    if http_proxy:
        ok_http = test_proxy(http_proxy, test_http_url)
        if ok_http:
            os.environ["HTTP_PROXY"] = http_proxy
            log.bind(tag=TAG).info(f"配置提供的Gemini HTTP代理连通成功: {http_proxy}")
        else:
            log.bind(tag=TAG).warning(f"配置提供的Gemini HTTP代理不可用: {http_proxy}")

    if https_proxy:
        ok_https = test_proxy(https_proxy, test_https_url)
        if ok_https:
            os.environ["HTTPS_PROXY"] = https_proxy
            log.bind(tag=TAG).info(f"配置提供的Gemini HTTPS代理连通成功: {https_proxy}")
        else:
            log.bind(tag=TAG).warning(
                f"配置提供的Gemini HTTPS代理不可用: {https_proxy}"
            )

    # 如果https_proxy不可用，但http_proxy可用且能走通https，则复用http_proxy作为https_proxy
    if ok_http and not ok_https:
        if test_proxy(http_proxy, test_https_url):
            os.environ["HTTPS_PROXY"] = http_proxy
            ok_https = True
            log.bind(tag=TAG).info(f"复用HTTP代理作为HTTPS代理: {http_proxy}")

    if not ok_http and not ok_https:
        log.bind(tag=TAG).error(
            f"Gemini 代理设置失败: HTTP 和 HTTPS 代理都不可用，请检查配置"
        )
        raise RuntimeError("HTTP 和 HTTPS 代理都不可用，请检查配置")


class LLMProvider(LLMProviderBase):
    def __init__(self, cfg: Dict[str, Any]):
        self.model_name = cfg.get("model_name", "gemini-2.0-flash")
        self.api_key = cfg["api_key"]
        http_proxy = cfg.get("http_proxy")
        https_proxy = cfg.get("https_proxy")

        if not check_model_key("LLM", self.api_key):
            raise ValueError("无效的Gemini API Key，请检查是否配置正确")

        if http_proxy or https_proxy:
            log.bind(tag=TAG).info(
                f"检测到Gemini代理配置，开始测试代理连通性和设置代理环境..."
            )
            setup_proxy_env(http_proxy, https_proxy)
            log.bind(tag=TAG).info(
                f"Gemini 代理设置成功 - HTTP: {http_proxy}, HTTPS: {https_proxy}"
            )
        
        # Initialize the new Google Gen AI client
        self.client = genai.Client(api_key=self.api_key)

    @staticmethod
    def _build_tools(funcs: List[Dict[str, Any]] | None):
        if not funcs:
            return None
        
        tools = []
        for f in funcs:
            func_decl = types.FunctionDeclaration(
                name=f["function"]["name"],
                description=f["function"]["description"],
                parameters=f["function"]["parameters"],
            )
            tools.append(func_decl)
        return tools

    # Gemini文档提到，无需维护session-id，直接用dialogue拼接而成
    def response(self, session_id, dialogue):
        try:
            yield from self._generate(dialogue, None)
        except GeneratorExit:
            # Generator was closed
            log.bind(tag=TAG).debug("Generator closed in response method")
            return

    def response_with_functions(self, session_id, dialogue, functions=None):
        try:
            log.bind(tag=TAG).debug(f"🔧 Gemini response_with_functions called - session_id: {session_id}")
            log.bind(tag=TAG).debug(f"🔧 - dialogue length: {len(dialogue) if dialogue else 0}")
            log.bind(tag=TAG).debug(f"🔧 - functions provided: {len(functions) if functions else 0}")
            
            tools = self._build_tools(functions)
            log.bind(tag=TAG).debug(f"🔧 - built tools: {len(tools) if tools else 0}")
            
            log.bind(tag=TAG).debug(f"🔧 开始调用_generate方法...")
            yield from self._generate(dialogue, tools, session_id)
            log.bind(tag=TAG).debug(f"🔧 _generate方法完成")
        except GeneratorExit:
            # Generator was closed
            log.bind(tag=TAG).debug("Generator closed in response_with_functions method")
            return

    def response_with_schema(self, system_prompt, user_prompt, json_schema):
        """支持JSON Schema的响应方法"""
        try:
            # 构造对话格式
            dialogue = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # 转换为Gemini格式
            contents = []
            for m in dialogue:
                role = m["role"]
                if role == "system":
                    system_content = f"System: {m.get('content', '')}"
                    contents.append(types.Content(
                        role="user",
                        parts=[types.Part.from_text(text=system_content)]
                    ))
                else:
                    mapped_role = "model" if role == "assistant" else "user"
                    contents.append(types.Content(
                        role=mapped_role,
                        parts=[types.Part.from_text(text=str(m.get("content", "")))]
                    ))

            # 配置生成设置，包含JSON Schema
            config = types.GenerateContentConfig(
                temperature=0.1,  # 降低温度以获得更一致的JSON输出
                top_p=0.9,
                top_k=40,
                max_output_tokens=2048,
                thinking_config=types.ThinkingConfig(thinking_budget=0),
                response_schema=json_schema,  # 设置JSON Schema
                response_mime_type="application/json"  # 强制JSON输出
            )

            # 生成内容
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=contents,
                config=config,
            )

            if hasattr(response, 'text') and response.text:
                return response.text
            else:
                return "{}"  # 返回空JSON作为fallback

        except Exception as e:
            log.bind(tag=TAG).error(f"Error in Gemini JSON Schema response: {e}")
            raise

    def _generate(self, dialogue, tools, session_id=None):  # `tools` is from _build_tools (function decls) or None
        generator_closed = False
        
        log.bind(tag=TAG).debug(f"⚙️ _generate方法开始 - tools: {len(tools) if tools else 0}")

        try:
            # Convert dialogue to the new SDK format
            contents = []
            for m in dialogue:
                role = m["role"]

                if role == "assistant" and "tool_calls" in m:
                    tc = m["tool_calls"][0]
                    # Function call content
                    func_call = types.FunctionCall(
                        name=tc["function"]["name"],
                        args=json.loads(tc["function"]["arguments"]),
                    )
                    contents.append(types.Content(
                        role="model",
                        parts=[types.Part.from_function_call(
                            name=func_call.name,
                            args=func_call.args
                        )]
                    ))
                    continue

                if role == "tool":
                    # Function response content - use function response format
                    # Extract tool_call_id if available for proper function response
                    tool_call_id = m.get("tool_call_id", "unknown")
                    content_text = str(m.get("content", ""))

                    # Find the corresponding function name from the previous assistant message
                    function_name = "unknown"
                    # Look backwards in dialogue to find the matching function call
                    for i in range(len(dialogue) - 1, -1, -1):
                        prev_msg = dialogue[i]
                        if (prev_msg.get("role") == "assistant" and
                            "tool_calls" in prev_msg and
                            prev_msg["tool_calls"]):
                            for tc in prev_msg["tool_calls"]:
                                if tc.get("id") == tool_call_id:
                                    function_name = tc.get("function", {}).get("name", "unknown")
                                    break
                            if function_name != "unknown":
                                break

                    # Create function response part with correct function name
                    function_response_part = types.Part.from_function_response(
                        name=function_name,  # Use actual function name, not tool_call_id
                        response={"result": content_text},
                    )

                    contents.append(types.Content(
                        role="user",  # Function responses should use user role
                        parts=[function_response_part]
                    ))
                    continue

                # Handle system role by converting to user role with system prefix
                if role == "system":
                    system_content = f"System: {m.get('content', '')}"
                    contents.append(types.Content(
                        role="user",
                        parts=[types.Part.from_text(text=system_content)]
                    ))
                    continue

                # Regular user/assistant content
                mapped_role = "model" if role == "assistant" else "user"
                contents.append(types.Content(
                    role=mapped_role,
                    parts=[types.Part.from_text(text=str(m.get("content", "")))]
                ))

            # Configure generation settings
            config = types.GenerateContentConfig(
                temperature=0.7,
                top_p=0.9,
                top_k=40,
                max_output_tokens=2048,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )

            # Add function tools if provided (removed Google Search as it's not supported)
            if tools:
                function_tool = types.Tool(
                    functionDeclarations=tools
                )
                config.tools = [function_tool]

            # 添加详细的提示词日志
            log.bind(tag=TAG).info(f"📝 最终发送给Gemini的完整提示词内容:")
            log.bind(tag=TAG).info(f"📝 - 模型: {self.model_name}")
            log.bind(tag=TAG).info(f"📝 - contents数量: {len(contents)}")
            
            # 显示完整的对话内容
            for i, content in enumerate(contents):
                role = content.role
                text_parts = []
                function_calls = []
                function_responses = []
                
                for part in content.parts:
                    if hasattr(part, 'text') and part.text:
                        text_parts.append(part.text)
                    elif hasattr(part, 'function_call') and part.function_call:
                        function_calls.append(f"{part.function_call.name}({part.function_call.args})")
                    elif hasattr(part, 'function_response') and part.function_response:
                        function_responses.append(f"{part.function_response.name}: {part.function_response.response}")
                
                content_summary = ""
                if text_parts:
                    content_summary += f"文本: {' '.join(text_parts)}"
                if function_calls:
                    content_summary += f" 函数调用: {', '.join(function_calls)}"
                if function_responses:
                    content_summary += f" 函数响应: {', '.join(function_responses)}"
                
                log.bind(tag=TAG).info(f"📝 Content[{i}] ({role}): {content_summary}")
            
            # 显示工具配置
            if tools:
                tool_names = [tool.name for tool in tools if tool and hasattr(tool, 'name')]
                log.bind(tag=TAG).info(f"🔧 可用工具列表: {tool_names}")
                
                # 显示每个工具的详细信息
                for tool in tools:
                    if tool and hasattr(tool, 'name'):
                        tool_desc = getattr(tool, 'description', 'No description')
                        log.bind(tag=TAG).info(f"🔧 工具定义 - {tool.name}: {tool_desc}")
                        if hasattr(tool, 'parameters') and tool.parameters:
                            log.bind(tag=TAG).info(f"🔧   参数: {tool.parameters}")
            else:
                log.bind(tag=TAG).info(f"🔧 无可用工具")
            
            # Use streaming generation
            log.bind(tag=TAG).debug(f"🌊 即将调用Gemini streaming API - model: {self.model_name}")
            log.bind(tag=TAG).debug(f"🌊 - contents数量: {len(contents)}")
            log.bind(tag=TAG).debug(f"🌊 - config: {config}")
            
            stream = self.client.models.generate_content_stream(
                model=self.model_name,
                contents=contents,
                config=config,
            )
            
            log.bind(tag=TAG).debug(f"🌊 Gemini streaming API调用成功，获得stream: {type(stream)}")
            log.bind(tag=TAG).debug(f"🌊 开始迭代stream chunks...")
            
            chunk_count = 0
            for chunk in stream:
                chunk_count += 1
                log.bind(tag=TAG).debug(f"🧩 收到第{chunk_count}个chunk: {type(chunk)}")
                try:
                    if hasattr(chunk, 'function_calls') and chunk.function_calls:
                        # Function call response
                        fc = chunk.function_calls[0]
                        log.bind(tag=TAG).info(f"🔧 Gemini返回函数调用: {fc.name}")
                        log.bind(tag=TAG).info(f"🔧 - 函数参数: {fc.args}")
                        
                        if fc.name.startswith('mcp_'):
                            log.bind(tag=TAG).info(f"🔧 ✅ 这是一个MCP函数调用: {fc.name}")
                        
                        yield None, [
                            SimpleNamespace(
                                id=uuid.uuid4().hex,
                                type="function",
                                function=SimpleNamespace(
                                    name=fc.name,
                                    arguments=json.dumps(fc.args, ensure_ascii=False),
                                ),
                            )
                        ]
                        return
                    elif hasattr(chunk, 'text') and chunk.text:
                        # Text response
                        yield chunk.text if tools is None else (chunk.text, None)
                except GeneratorExit:
                    # Generator was closed during chunk processing
                    log.bind(tag=TAG).debug("Generator closed during chunk processing")
                    generator_closed = True
                    return

            log.bind(tag=TAG).debug(f"🏁 Gemini stream遍历完成，总共处理了{chunk_count}个chunks")
            
            # 如果有工具可用但Gemini没有调用任何函数，记录这一点
            if tools and chunk_count > 0:
                log.bind(tag=TAG).info(f"🔧 注意: Gemini有{len(tools)}个可用工具，但没有调用任何函数")
                log.bind(tag=TAG).info(f"🔧 这可能表示:")
                log.bind(tag=TAG).info(f"🔧 1. 用户的问题不需要调用工具")
                log.bind(tag=TAG).info(f"🔧 2. 工具描述不够清晰，Gemini没有识别出需要调用")
                log.bind(tag=TAG).info(f"🔧 3. 工具名称或参数有问题")
            
            if chunk_count == 0:
                log.bind(tag=TAG).warning(f"⚠️ 警告：Gemini stream没有返回任何chunk，可能存在问题")
                log.bind(tag=TAG).warning(f"⚠️ - session_id: {session_id}")
                log.bind(tag=TAG).warning(f"⚠️ - dialogue长度: {len(dialogue) if dialogue else 0}")
                log.bind(tag=TAG).warning(f"⚠️ - tools数量: {len(tools) if tools else 0}")
                log.bind(tag=TAG).warning(f"⚠️ - model_name: {self.model_name}")
                log.bind(tag=TAG).warning(f"⚠️ - client类型: {type(self.client)}")
                
                # 记录dialogue内容供调试
                if dialogue:
                    log.bind(tag=TAG).warning(f"⚠️ - dialogue最后一条: {dialogue[-1] if dialogue else 'None'}")
                    
                # 记录config信息
                if hasattr(self, 'generation_config'):
                    log.bind(tag=TAG).warning(f"⚠️ - generation_config: {self.generation_config}")
                    
                return  # 避免继续执行后续逻辑

        except GeneratorExit:
            # Generator was closed during stream processing
            log.bind(tag=TAG).debug("Generator closed during stream processing")
            generator_closed = True
            return
        except Exception as e:
            log.bind(tag=TAG).error(f"🚨 Gemini API调用异常: {type(e).__name__}: {e}")
            log.bind(tag=TAG).error(f"🚨 - session_id: {session_id}")
            log.bind(tag=TAG).error(f"🚨 - model_name: {self.model_name}")
            log.bind(tag=TAG).error(f"🚨 - dialogue长度: {len(dialogue) if dialogue else 0}")
            log.bind(tag=TAG).error(f"🚨 - tools数量: {len(tools) if tools else 0}")
            
            # 记录完整的异常堆栈以便调试
            import traceback
            log.bind(tag=TAG).error(f"🚨 异常堆栈:\n{traceback.format_exc()}")
            raise

        finally:
            # Only try to yield if generator wasn't closed
            if not generator_closed and tools is not None:
                try:
                    yield None, None  # function‑mode 结束，返回哑包
                except GeneratorExit:
                    # Generator was closed, don't try to yield
                    log.bind(tag=TAG).debug("Generator closed in finally block")
                    pass
